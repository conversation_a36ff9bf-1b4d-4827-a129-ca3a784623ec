package com.weihengtech.ecos.service.ele.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Pair;
import com.weihengtech.ecos.api.pojo.dtos.EleDayAheadPriceDto;
import com.weihengtech.ecos.api.pojo.vos.EleHomeStrategyPreviewVO;
import com.weihengtech.ecos.api.pojo.vos.EleStrategyPreviewVO;
import com.weihengtech.ecos.consts.CommonConstants;
import com.weihengtech.ecos.consts.TsdbMetricsConstants;
import com.weihengtech.ecos.enums.ele.ChargeTypeEnum;
import com.weihengtech.ecos.model.dos.HybridSinglePhaseDO;
import com.weihengtech.ecos.model.dtos.customize.ChargingStructDTO;
import com.weihengtech.ecos.model.dtos.customize.CustomizeInfoEzDto;
import com.weihengtech.ecos.model.dtos.ele.EleStrategyDTO;
import com.weihengtech.ecos.service.ele.ClientEleStrategyService;
import com.weihengtech.ecos.service.global.StrategyService;
import com.weihengtech.ecos.service.global.TimeSeriesDatabaseService;
import com.weihengtech.ecos.service.thirdpart.HubService;
import com.weihengtech.ecos.utils.AsyncResultUtil;
import com.weihengtech.ecos.utils.EleStrategyUtil;
import com.weihengtech.ecos.utils.TimeUtil;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 日前电价策略服务
 *
 * <AUTHOR>
 * @date 2024/10/25 16:44
 * @version 1.0
 */
@Service
public class ClientEleStrategyServiceImpl implements ClientEleStrategyService {

    /**
     * 峰谷价差
     */
    private static final Double DIFF = 0.05d;

    @Resource
    private HubService hubService;
    @Resource
    private StrategyService strategyService;
    @Resource
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Override
    public CustomizeInfoEzDto queryStrategy(List<EleStrategyDTO> eleStrategyList, String timezone) {
        if (CollUtil.isEmpty(eleStrategyList) || eleStrategyList.size() != 24) {
            return new CustomizeInfoEzDto();
        }
        // 如果全部都是补充不放，即清除12充放
        boolean allNoneType = eleStrategyList.stream()
                .allMatch(i -> ChargeTypeEnum.NONE_CHARGE.getType() == i.getChargeType());
        if (allNoneType) {
            return buildClearData(timezone);
        }
        List<ChargingStructDTO> chargeList = calTimeList(eleStrategyList, timezone, ChargeTypeEnum.CHARGE.getType());
        List<ChargingStructDTO> dischargeList = calTimeList(eleStrategyList, timezone, ChargeTypeEnum.DISCHARGE.getType());
        return CustomizeInfoEzDto.builder()
                .chargingList(chargeList)
                .dischargingList(dischargeList)
                .build();
    }

    /**
     * 构建清除12充放的数据
     *
     * @param timezone 时区
     * @return
     */
    private CustomizeInfoEzDto buildClearData(String timezone) {
        // 获取UTC时区的当前日期的0点
        ZonedDateTime utcMidnight = ZonedDateTime.now(ZoneId.of("UTC"))
                .withHour(0)
                .withMinute(0)
                .withSecond(0)
                .withNano(0);

        // 转换为指定时区的时间
        ZonedDateTime localTime = utcMidnight.withZoneSameInstant(ZoneId.of(timezone));
        List<ChargingStructDTO> chargeList = new ArrayList<>();
        List<ChargingStructDTO> dischargeList = new ArrayList<>();
        for (int i = 0; i < 12; i++) {
            chargeList.add(new ChargingStructDTO()
                    .withStartHour(localTime.getHour())
                    .withEndHour(localTime.getHour())
                    .withStartMinute(localTime.getMinute())
                    .withEndMinute(localTime.getMinute())
                    .withPower(0));
        }
        for (int i = 0; i < 12; i++) {
            dischargeList.add(new ChargingStructDTO()
                    .withStartHour(localTime.getHour())
                    .withEndHour(localTime.getHour())
                    .withStartMinute(localTime.getMinute())
                    .withEndMinute(localTime.getMinute())
                    .withPower(0));
        }
        return CustomizeInfoEzDto.builder()
                .chargingList(chargeList)
                .dischargingList(dischargeList)
                .build();
    }

    @Override
    public List<EleStrategyDTO> calStrategyWithNormal(List<EleDayAheadPriceDto> priceDataList, EleStrategyPreviewVO param) {
        if (CollUtil.isEmpty(priceDataList)) {
            return Collections.emptyList();
        }
        // 根据电价数据计算平均值、标准差、变异系数
        List<BigDecimal> priceList = priceDataList.stream()
                .map(EleDayAheadPriceDto::getAverage)
                .collect(Collectors.toList());
        double average = priceList.stream().collect(Collectors.averagingDouble(BigDecimal::doubleValue));
        double standardDeviation = calStandardDeviation(priceList, average);
        double variation = Math.abs(standardDeviation / average);
        double chargeThreshold = average * (1 - calThreshold(variation));
        double dischargeThreshold = average * (1 + calThreshold(variation));
        long negativeHourCount = priceDataList.stream()
                .filter(i -> i.getAverage().compareTo(BigDecimal.ZERO) < 0)
                .count();
        return calEleStrategyPreviewData(priceDataList, chargeThreshold, dischargeThreshold, param, negativeHourCount);
    }

    @Override
    public Map<Integer, Integer> homePowerGraph(EleHomeStrategyPreviewVO param) {
        HybridSinglePhaseDO deviceInfo = hubService.getById(param.getDeviceId());
        // 1、计算起始时间，今天和明天都按照今天起始算（由于今日没结束，计算明日的策略无法将今天的数据纳入）
        String timezoneStr = param.getTimezone();
        Long startTime = -1 == param.getTime() ? TimeUtil.getLastDayStart(timezoneStr) : TimeUtil.getDayStart(0, timezoneStr);
        // 2、计算过去10个工作日时间区间，如果是周末，则计算过去5个周末
        List<Pair<Long, Long>> backDaysList;
        if (0 == param.getDayType()) {
            backDaysList = TimeUtil.calWeekendDays(startTime, timezoneStr, 5);
        } else {
            backDaysList = TimeUtil.calWorkDays(startTime, timezoneStr, 10);
        }
        // 3、获取各个时间区间的家庭功率数据，每个时间区间24个点（降采样取平均值）
        List<Map<Integer, Integer>> allDayPowers = AsyncResultUtil.multiThreadDone(backDaysList,
                k -> queryHomeLoaderPower(deviceInfo, k, timezoneStr), threadPoolTaskExecutor);
        if (allDayPowers.stream().allMatch(CollUtil::isEmpty)) {
            return Collections.emptyMap();
        }
        // 4、每个点按照10个工作日中同一时间的点计算平均值
        return EleStrategyUtil.calAverage(allDayPowers);
    }

    @Override
    public void calStrategyWithHomePower(List<EleStrategyDTO> eleStrategyRes, Map<Integer, Integer> eleHomePowerRes, String timezone) {
        if (CollUtil.isEmpty(eleHomePowerRes)) {
            return;
        }
        // 找出连续2小时放电以上的时间段
        List<List<EleStrategyDTO>> dischargeList = new ArrayList<>();
        List<EleStrategyDTO> partial = new ArrayList<>();
        int i = 0, j = 0;
        while (i < eleStrategyRes.size() && j <= eleStrategyRes.size()) {
            if (j == eleStrategyRes.size()) {
                if (j - i > 2) {
                    dischargeList.add(partial);
                }
                if (!partial.isEmpty()) {
                    partial.clear();
                }
                i = j;
                continue;
            }
            EleStrategyDTO point = eleStrategyRes.get(j);
            if (point.getChargeType() == ChargeTypeEnum.DISCHARGE.getType()) {
                partial.add(point);
                j++;
            }else {
                if (j != i) {
                    if (j - i > 2) {
                        dischargeList.add(new ArrayList<>(partial));
                    }
                    if (!partial.isEmpty()) {
                        partial.clear();
                    }
                    i = j;
                } else {
                    i ++;
                    j ++;
                }
            }
        }
        if (CollUtil.isEmpty(dischargeList)) {
            return;
        }
        Map<Long, Boolean> dischargeMap = calDischargeFlag(dischargeList, eleHomePowerRes, timezone);
        eleStrategyRes.forEach(k -> {
            if (dischargeMap.containsKey(k.getStartTimeUnix()) && !dischargeMap.get(k.getStartTimeUnix())) {
                k.setChargeType(ChargeTypeEnum.NONE_CHARGE.getType());
                k.setPower(0);
            }
            k.setHomePower(eleHomePowerRes.get(TimeUtil.calCurHour(k.getStartTimeUnix(), timezone)));
        });
    }

    @Override
    public List<EleStrategyDTO> calStrategyWithEarning(List<EleDayAheadPriceDto> priceDataList,
                                                       EleStrategyPreviewVO param) {
        if (CollUtil.isEmpty(priceDataList)) {
            return Collections.emptyList();
        }

        Double diff = param.getPriceGap().doubleValue();

        // 根据电价数据计算平均值、标准差、变异系数
        List<BigDecimal> priceList = priceDataList.stream()
                .map(EleDayAheadPriceDto::getAverage)
                .collect(Collectors.toList());
        double average = priceList.stream().collect(Collectors.averagingDouble(BigDecimal::doubleValue));
        double standardDeviation = calStandardDeviation(priceList, average);
        double variation = Math.abs(standardDeviation / average);
        double chargeThreshold = average * (1 - calThreshold(variation));
        double dischargeThreshold = average * (1 + calThreshold(variation));
        List<EleStrategyDTO> eleStrategyList = calEleStrategyPreviewData(priceDataList, chargeThreshold,
                dischargeThreshold, param, 0L);
        /*
         * 1、在符合条件的充电电价和放电电价中分别获取最大的充电电价 Pc(max) 和最小的放电电价 Pd(min)；
         * 计算 峰谷价差ΔP = Pd(min) - [ Pc(max) + Ptax ]，Ptax为购电税费；
         * 若ΔP大于等于0.05欧元/度，则当前初始充放电策略即满足收益要求
         */
        boolean resP = dischargeThreshold - (chargeThreshold + param.getPurchaseTax().doubleValue()) >= diff.floatValue();
        if (resP) {
            return eleStrategyList;
        }
        /*
         * 2、取符合条件的所有初始充电电价和放电电价，分别计算充电电价平均值AvgC和放电电价平均值AvgD;
         * 计算 峰谷平均值价差ΔAvg = AvgD - ( AvgC + Ptax)，Ptax为购电税费；
         * 若ΔAvg大于等于0.05欧元/度，取大于等于AcgD的电价时段放电，小于等于AvgC的电价时段充电；
         */
        double chargeAverage = eleStrategyList.stream()
                .filter(i -> ChargeTypeEnum.CHARGE.getType() == i.getChargeType())
                .map(EleStrategyDTO::getPrice)
                .collect(Collectors.averagingDouble(BigDecimal::doubleValue));
        double dischargeAverage = eleStrategyList.stream()
                .filter(i -> ChargeTypeEnum.DISCHARGE.getType() == i.getChargeType())
                .map(EleStrategyDTO::getPrice)
                .collect(Collectors.averagingDouble(BigDecimal::doubleValue));
        boolean resAvg = dischargeAverage - (chargeAverage + param.getPurchaseTax().doubleValue()) >= diff;
        if (resAvg) {
            for (EleStrategyDTO eleStrategy : eleStrategyList) {
                if (ChargeTypeEnum.CHARGE.getType() == eleStrategy.getChargeType()
                        && eleStrategy.getPrice().doubleValue() > chargeAverage) {
                    eleStrategy.setChargeType(ChargeTypeEnum.NONE_CHARGE.getType());
                    eleStrategy.setPower(0);
                } else if (ChargeTypeEnum.DISCHARGE.getType() == eleStrategy.getChargeType()
                        && eleStrategy.getPrice().doubleValue() < dischargeAverage) {
                    eleStrategy.setChargeType(ChargeTypeEnum.NONE_CHARGE.getType());
                    eleStrategy.setPower(0);
                }
            }
            return eleStrategyList;
        }
        /*
         * 3、取当天中的最高电价Pmax 和最低电价Pmin；
         * 计算最值峰谷价差ΔPm = Pmax - ( Pmin+ Ptax )，Ptax为购电税费；
         * 若ΔPm大于等于0.05欧元/度，取最高电价时段放电，最低电价时段充电；
         */
        double maxPrice = priceDataList.stream()
                .max(Comparator.comparing(EleDayAheadPriceDto::getAverage))
                .map(EleDayAheadPriceDto::getAverage)
                .map(BigDecimal::doubleValue)
                .orElse(0d);
        double minPrice = priceDataList.stream()
                .min(Comparator.comparing(EleDayAheadPriceDto::getAverage))
                .map(EleDayAheadPriceDto::getAverage)
                .map(BigDecimal::doubleValue)
                .orElse(0d);
        boolean resPm = maxPrice - (minPrice + param.getPurchaseTax().doubleValue()) > diff;
        if (resPm) {
            for (EleStrategyDTO eleStrategy : eleStrategyList) {
                if (ChargeTypeEnum.CHARGE.getType() == eleStrategy.getChargeType()
                        && eleStrategy.getPrice().doubleValue() > minPrice) {
                    eleStrategy.setChargeType(ChargeTypeEnum.NONE_CHARGE.getType());
                    eleStrategy.setPower(0);
                } else if (ChargeTypeEnum.DISCHARGE.getType() == eleStrategy.getChargeType()
                        && eleStrategy.getPrice().doubleValue() < maxPrice) {
                    eleStrategy.setChargeType(ChargeTypeEnum.NONE_CHARGE.getType());
                    eleStrategy.setPower(0);
                }
            }
            return eleStrategyList;
        }
        /*
         * 判断是否存在负电价？若不存在，则全天执行自发自用模式，清空充放电时间段；
         * 若存在负电价，将所有负电价取绝对值，然后判断这些绝对值中是否存在大于等于Ptax的（判断是否存在加上税费后的电价还是不大于0）？
         * 若存在，则满足条件的负电价时段设置充电策略；
         * 若不存在，则全天执行自发自用模式，清空充放电时间段；
         */
        List<EleDayAheadPriceDto> negativePriceList = priceDataList.stream()
                .filter(i -> i.getAverage().compareTo(BigDecimal.ZERO) < 0)
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(negativePriceList)) {
            for (EleStrategyDTO eleStrategy : eleStrategyList) {
                eleStrategy.setChargeType(ChargeTypeEnum.NONE_CHARGE.getType());
                eleStrategy.setPower(0);
            }
            return eleStrategyList;
        }
        Set<Long> chargeTimeSet = negativePriceList.stream()
                .filter(i -> i.getAverage().add(param.getPurchaseTax()).compareTo(BigDecimal.ZERO) < 0)
                .map(EleDayAheadPriceDto::getStartTimeUnix)
                .collect(Collectors.toSet());
        if (CollUtil.isNotEmpty(chargeTimeSet)) {
            for (EleStrategyDTO eleStrategy : eleStrategyList) {
                if (chargeTimeSet.contains(eleStrategy.getStartTimeUnix())) {
                    eleStrategy.setChargeType(ChargeTypeEnum.CHARGE.getType());
                } else {
                    eleStrategy.setChargeType(ChargeTypeEnum.NONE_CHARGE.getType());
                    eleStrategy.setPower(0);
                }
            }
            return eleStrategyList;
        }
        for (EleStrategyDTO eleStrategy : eleStrategyList) {
            eleStrategy.setChargeType(ChargeTypeEnum.NONE_CHARGE.getType());
            eleStrategy.setPower(0);
        }
        return eleStrategyList;
    }

    @Override
    public List<EleStrategyDTO> calStrategyWithNegative(List<EleDayAheadPriceDto> elePriceList, EleStrategyPreviewVO param) {
        if (CollUtil.isEmpty(elePriceList)) {
            return Collections.emptyList();
        }

        // 计算充电功率：额定功率的一半
        Integer chargePower = param.getRatedPower() / 2;

        // 遍历电价数据，找出负电价时段并设置充电策略
        return elePriceList.stream().map(priceData -> {
            // 计算实际电价：average + tax
            BigDecimal actualPrice = priceData.getAverage();
            if (priceData.getTax() != null) {
                actualPrice = actualPrice.add(priceData.getTax());
            }

            // 判断是否为负电价时段
            if (actualPrice.compareTo(BigDecimal.ZERO) < 0) {
                // 负电价时段：设置充电，功率为额定功率一半，开启弃光
                return EleStrategyDTO.builder()
                        .startTimeUnix(priceData.getStartTimeUnix())
                        .price(priceData.getAverage())
                        .power(chargePower)
                        .chargeType(ChargeTypeEnum.CHARGE.getType())
                        .abandonPv(1) // 开启弃光
                        .build();
            } else {
                // 非负电价时段：不充不放
                return EleStrategyDTO.builder()
                        .startTimeUnix(priceData.getStartTimeUnix())
                        .price(priceData.getAverage())
                        .power(0)
                        .chargeType(ChargeTypeEnum.NONE_CHARGE.getType())
                        .abandonPv(0) // 不弃光
                        .build();
            }
        }).collect(Collectors.toList());
    }

    /** 计算放电区间中，满足家庭负载条件的区间，满足条件的设置true，其他false */
    private Map<Long, Boolean> calDischargeFlag(List<List<EleStrategyDTO>> dischargeList,
                                                Map<Integer, Integer> homePowerMap, String timezone) {
        Map<Long, Boolean> dischargeMap = new HashMap<>();
        for (List<EleStrategyDTO> eleStrategyList : dischargeList) {
            boolean allContains = eleStrategyList.stream().allMatch(i ->
                    homePowerMap.containsKey(TimeUtil.calCurHour(i.getStartTimeUnix(), timezone)));
            if (!allContains) {
                continue;
            }
            for (EleStrategyDTO eleStrategy : eleStrategyList) {
                eleStrategy.setHomePower(homePowerMap.get(TimeUtil.calCurHour(eleStrategy.getStartTimeUnix(), timezone)));
            }
            eleStrategyList.sort(Comparator.comparing(EleStrategyDTO::getHomePower));
            for (int i = 0; i < eleStrategyList.size(); i++) {
                EleStrategyDTO eleStrategy = eleStrategyList.get(i);
                if (i == eleStrategyList.size() - 2 || i == eleStrategyList.size() - 1) {
                    dischargeMap.put(eleStrategy.getStartTimeUnix(), true);
                } else {
                    dischargeMap.put(eleStrategy.getStartTimeUnix(), false);
                }
            }
        }
        return dischargeMap;
    }

    /** 查询每日的家庭负载功率<小时数，功率值> */
    private Map<Integer, Integer> queryHomeLoaderPower(HybridSinglePhaseDO deviceInfo, Pair<Long, Long> timePair, String timezone) {
        // 查询24小时降采样的相关点位的功率数据
        TimeSeriesDatabaseService timeSeriesDatabaseService = strategyService.chooseTimeSeriesDatabaseService(deviceInfo);
        Map<String, LinkedHashMap<Long, Object>> result = timeSeriesDatabaseService.meanQuery(deviceInfo.getDeviceSn(), CommonConstants.TH_REALTIME_POWER_V2,
                timePair.getKey(), timePair.getValue(), 60);
        // 多个点位计算出家庭负载功率
        return packageNowDeviceRealtimeResult(result, timezone);

    }

    /** 多个点位计算出家庭负载功率<小时数，功率值> */
    public Map<Integer, Integer> packageNowDeviceRealtimeResult(Map<String, LinkedHashMap<Long, Object>> metricMap, String timezone) {

        List<Long> sortedTimeList = new ArrayList<>();
        List<Object> meterValueList = new ArrayList<>();
        List<Object> solarValueList1 = new ArrayList<>();
        List<Object> solarValueList2 = new ArrayList<>();
        List<Object> batteryValueList = new ArrayList<>();
        for (String metric : metricMap.keySet()) {
            LinkedHashMap<Long, Object> resultMap = metricMap.get(metric);
            switch (metric) {
                case TsdbMetricsConstants.METER_P_PV:
                    solarValueList1 = resultMap.keySet().stream().sorted().map(resultMap::get).collect(Collectors.toList());
                    break;
                case TsdbMetricsConstants.METER_PV_P:
                    solarValueList2 = resultMap.keySet().stream().sorted().map(resultMap::get).collect(Collectors.toList());
                    break;
                case TsdbMetricsConstants.BAT_P:
                    batteryValueList = resultMap.keySet().stream().sorted().map(resultMap::get).collect(Collectors.toList());
                    break;
                case TsdbMetricsConstants.METER_P:
                    sortedTimeList = resultMap.keySet().stream().sorted().collect(Collectors.toList());
                    meterValueList = resultMap.keySet().stream().sorted().map(resultMap::get).collect(Collectors.toList());
                    break;
                default:
            }
        }
        if (CollUtil.isEmpty(sortedTimeList)) {
            return Collections.emptyMap();
        }
        Map<Integer, Integer> homePowerDps = new HashMap<>();
        // 家庭用电功率:meter_p(电网) + meter_p_pv(光伏) - bat_p(电池)
        for (int i = 0; i < sortedTimeList.size(); i++) {
            Long time = sortedTimeList.get(i);
            BigDecimal solarValue1 = parseListValue(i, solarValueList1);
            BigDecimal solarValue2 = parseListValue(i, solarValueList2);
            BigDecimal solarValue = cn.hutool.core.util.NumberUtil.add(solarValue1, solarValue2);
            BigDecimal meterValue = parseListValue(i, meterValueList);
            BigDecimal batteryValue = parseListValue(i, batteryValueList);
            BigDecimal homePower = cn.hutool.core.util.NumberUtil.sub(cn.hutool.core.util.NumberUtil.add(meterValue, solarValue), batteryValue);
            // 时序数据库算出的数据是从1：00-23:59，只需要减去1s，求出当前小时数，即可调整为00:00-23:00
            if (homePower.intValue() > 0) {
                homePowerDps.put(TimeUtil.calCurHour(time-1, timezone), homePower.intValue());
            }
        }
        return homePowerDps;
    }

    /** 解析点位值数据 */
    private BigDecimal parseListValue(Integer i, List<Object> list) {
        return new BigDecimal(i > list.size() - 1 ? "0" : Optional.ofNullable(list.get(i)).orElse("0").toString());
    }

    /** 计算预览电价策略数据 */
    private List<EleStrategyDTO> calEleStrategyPreviewData(List<EleDayAheadPriceDto> priceDataList, double chargeThreshold,
                                                           double dischargeThreshold, EleStrategyPreviewVO param, Long negativeHourCount) {
        // 如果负电价时段大于4小时，全部负电价为充电时段，其余场景还是按照阈值计算充放电时段
        return priceDataList.stream().map(i -> {
            if (i.getAverage().doubleValue() < (negativeHourCount > 4 ? 0 : chargeThreshold)) {
                return EleStrategyDTO.builder()
                        .startTimeUnix(i.getStartTimeUnix())
                        .price(i.getAverage())
                        .power(Optional.ofNullable(param.getDefChargePower()).orElse(param.getRatedPower()/2))
                        .chargeType(ChargeTypeEnum.CHARGE.getType())
                        .build();
            } else if (i.getAverage().compareTo(BigDecimal.ZERO) > 0 && i.getAverage().doubleValue() > dischargeThreshold) {
                return EleStrategyDTO.builder()
                        .startTimeUnix(i.getStartTimeUnix())
                        .price(i.getAverage())
                        .power(Optional.ofNullable(param.getDefDischargePower()).orElse(param.getRatedPower()))
                        .chargeType(ChargeTypeEnum.DISCHARGE.getType())
                        .build();
            } else {
                return EleStrategyDTO.builder()
                        .startTimeUnix(i.getStartTimeUnix())
                        .price(i.getAverage())
                        .power(0)
                        .chargeType(ChargeTypeEnum.NONE_CHARGE.getType())
                        .build();
            }
        }).collect(Collectors.toList());
    }

    /** 计算放电时间策略：时间段合并 */
    private List<ChargingStructDTO> calTimeList(List<EleStrategyDTO> eleStrategyList, String timezone, Integer chargeType) {
        List<ChargingStructDTO> timeList = new ArrayList<>();
        int i = 0, j = 0;
        while (i < eleStrategyList.size() && j <= eleStrategyList.size()) {
            if (j == eleStrategyList.size()) {
                timeList.add(new ChargingStructDTO()
                        .withStartHour(ZonedDateTime.ofInstant(Instant.ofEpochSecond(eleStrategyList.get(i).getStartTimeUnix()), ZoneId.of(timezone)).getHour())
                        .withStartMinute(0)
                        .withEndHour(ZonedDateTime.ofInstant(Instant.ofEpochSecond(eleStrategyList.get(j-1).getStartTimeUnix()+3600), ZoneId.of(timezone)).getHour())
                        .withEndMinute(0)
                        .withPower(eleStrategyList.get(i).getPower())
                        .withAbandonPv(eleStrategyList.get(i).getAbandonPv()));
                i = j;
                continue;
            }
            EleStrategyDTO item = eleStrategyList.get(j);
            if (Objects.equals(chargeType, item.getChargeType())) {
                j++;
            }else {
                if (i != j) {
                    timeList.add(new ChargingStructDTO()
                            .withStartHour(ZonedDateTime.ofInstant(Instant.ofEpochSecond(eleStrategyList.get(i).getStartTimeUnix()), ZoneId.of(timezone)).getHour())
                            .withStartMinute(0)
                            .withEndHour(ZonedDateTime.ofInstant(Instant.ofEpochSecond(eleStrategyList.get(j).getStartTimeUnix()), ZoneId.of(timezone)).getHour())
                            .withEndMinute(0)
                            .withPower(eleStrategyList.get(i).getPower())
                            .withAbandonPv(eleStrategyList.get(i).getAbandonPv()));
                    i = j;
                } else {
                    i ++;
                    j ++;
                }
            }
        }
        return timeList;
    }

    /**
     * 求标准差
     *
     * @param list 数据集
     * @param average 平均值
     * @return 标准差
     */
    private double calStandardDeviation(List<BigDecimal> list, Double average) {
        double variance = 0.0;
        double stdDeviation;
        for (BigDecimal num : list) {
            variance += Math.pow(num.doubleValue() - average, 2);
        }
        variance /= list.size();
        stdDeviation = Math.sqrt(variance);
        return stdDeviation;
    }

    /**
     * 计算阈值
     *
     * @param variation 变异系数
     * @return 阈值
     */
    private double calThreshold(double variation) {
        if (variation >= 0 && variation < 0.1) {
            return 0.05;
        } else if (variation >= 0.1 && variation < 0.2) {
            return 0.1;
        } else if (variation >= 0.2 && variation < 0.3) {
            return 0.15;
        } else if (variation >= 0.3 && variation < 0.4) {
            return 0.2;
        } else if (variation >= 0.4 && variation < 0.5) {
            return 0.25;
        } else if (variation >= 0.5 && variation < 0.6) {
            return 0.3;
        } else if (variation >= 0.6 && variation < 0.7) {
            return 0.35;
        } else if (variation >= 0.7 && variation < 0.8) {
            return 0.4;
        } else if (variation >= 0.8 && variation < 0.9) {
            return 0.45;
        } else if (variation >= 0.9 && variation < 1.0) {
            return 0.5;
        } else {
            return 0.55;
        }
    }
}
